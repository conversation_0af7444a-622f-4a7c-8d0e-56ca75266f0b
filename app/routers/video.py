"""
Video processing API endpoints
"""

from fastapi import APIRouter, HTTPException, Query, status

from app.models.video import (
    ScreenshotRequest,
    DurationResponse,
    ScreenshotResponse,
    ThumbnailResponse,
    ErrorResponse
)
from app.services.video_service import VideoService, VideoProcessingError

router = APIRouter()
video_service = VideoService()


@router.get(
    "/duration",
    response_model=DurationResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid URL or video processing error"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    summary="Get video duration",
    description="Extract and return the total duration of a video from a given URL"
)
async def get_video_duration(
    url: str = Query(..., description="URL of the video to analyze")
):
    """
    Get the duration of a video from a URL.
    
    - **url**: Valid video URL (HTTP/HTTPS)
    
    Returns the duration in seconds and formatted as HH:MM:SS
    """
    try:
        duration, formatted_duration = video_service.get_video_duration(url)
        return DurationResponse(
            duration=duration,
            formatted_duration=formatted_duration
        )
    except VideoProcessingError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.post(
    "/screenshot",
    response_model=ScreenshotResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid URL, timestamp, or video processing error"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    summary="Capture video screenshot",
    description="Capture a screenshot from a video at a specific timestamp"
)
async def capture_screenshot(request: ScreenshotRequest):
    """
    Capture a screenshot from a video at a specific timestamp.

    - **url**: Valid video URL (HTTP/HTTPS)
    - **timestamp**: Time in seconds where to capture the screenshot (must be >= 0)

    Returns the screenshot as base64 encoded PNG image data
    """
    try:
        image_base64, actual_timestamp = video_service.capture_screenshot(
            str(request.url),
            request.timestamp
        )
        return ScreenshotResponse(
            success=True,
            image_base64=image_base64,
            timestamp=actual_timestamp,
            message="Screenshot captured successfully"
        )
    except VideoProcessingError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )


@router.get(
    "/thumbnail",
    response_model=ThumbnailResponse,
    responses={
        400: {"model": ErrorResponse, "description": "Invalid URL or video processing error"},
        500: {"model": ErrorResponse, "description": "Internal server error"}
    },
    summary="Generate video thumbnail",
    description="Generate a thumbnail from the middle of a video (50% of total duration)"
)
async def generate_thumbnail(
    url: str = Query(..., description="URL of the video to generate thumbnail from")
):
    """
    Generate a thumbnail from the middle of a video.

    - **url**: Valid video URL (HTTP/HTTPS)

    Automatically captures a screenshot at 50% of the video's total duration and returns as base64 encoded PNG
    """
    try:
        image_base64, timestamp, duration = video_service.generate_thumbnail(url)
        return ThumbnailResponse(
            success=True,
            image_base64=image_base64,
            timestamp=timestamp,
            duration=duration,
            message="Thumbnail generated successfully"
        )
    except VideoProcessingError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}"
        )



