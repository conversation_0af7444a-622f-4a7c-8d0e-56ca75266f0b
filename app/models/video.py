"""
Pydantic models for video processing API
"""

from pydantic import BaseModel, HttpUrl, Field
from typing import Optional


class VideoUrlRequest(BaseModel):
    """Request model for video URL"""
    url: HttpUrl = Field(..., description="URL of the video to process")


class ScreenshotRequest(BaseModel):
    """Request model for screenshot capture"""
    url: HttpUrl = Field(..., description="URL of the video to process")
    timestamp: float = Field(..., ge=0, description="Timestamp in seconds where to capture screenshot")


class DurationResponse(BaseModel):
    """Response model for video duration"""
    duration: float = Field(..., description="Video duration in seconds")
    formatted_duration: str = Field(..., description="Human-readable duration (HH:MM:SS)")


class ScreenshotResponse(BaseModel):
    """Response model for screenshot capture"""
    success: bool = Field(..., description="Whether screenshot was captured successfully")
    image_base64: str = Field(..., description="Base64 encoded PNG image data")
    timestamp: float = Field(..., description="Actual timestamp used for screenshot")
    message: Optional[str] = Field(None, description="Additional information")


class ThumbnailResponse(BaseModel):
    """Response model for thumbnail generation"""
    success: bool = Field(..., description="Whether thumbnail was generated successfully")
    image_base64: str = Field(..., description="Base64 encoded PNG image data")
    timestamp: float = Field(..., description="Timestamp used for thumbnail (middle of video)")
    duration: float = Field(..., description="Total video duration")
    message: Optional[str] = Field(None, description="Additional information")


class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
