"""
FastAPI Video Processing Service

A service for processing videos using FFmpeg with endpoints for:
- Getting video duration
- Capturing screenshots at specific timestamps
- Generating thumbnails from video middle point
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.routers import video

app = FastAPI(
    title="Video Processing API",
    description="A service for video processing using FFmpeg",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(video.router, prefix="/video", tags=["video"])


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "Video Processing API is running"}


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}
