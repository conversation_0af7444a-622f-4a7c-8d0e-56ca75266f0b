"""
Video processing service using FFmpeg
"""

import os
import tempfile
import uuid
from datetime import timed<PERSON><PERSON>
from typing import Tuple
import ffmpeg
from urllib.parse import urlparse


class VideoProcessingError(Exception):
    """Custom exception for video processing errors"""
    pass


class VideoService:
    """Service for video processing operations using FFmpeg"""
    
    def __init__(self, output_dir: str = "outputs"):
        """
        Initialize video service
        
        Args:
            output_dir: Directory to store output files
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def _validate_url(self, url: str) -> None:
        """
        Validate video URL
        
        Args:
            url: Video URL to validate
            
        Raises:
            VideoProcessingError: If URL is invalid
        """
        parsed = urlparse(str(url))
        if not parsed.scheme or not parsed.netloc:
            raise VideoProcessingError("Invalid URL format")
    
    def _format_duration(self, seconds: float) -> str:
        """
        Format duration in seconds to HH:MM:SS format
        
        Args:
            seconds: Duration in seconds
            
        Returns:
            Formatted duration string
        """
        td = timedelta(seconds=seconds)
        hours, remainder = divmod(td.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
    
    def get_video_duration(self, url: str) -> Tuple[float, str]:
        """
        Get video duration using FFmpeg
        
        Args:
            url: Video URL
            
        Returns:
            Tuple of (duration_in_seconds, formatted_duration)
            
        Raises:
            VideoProcessingError: If unable to get duration
        """
        try:
            self._validate_url(url)
            
            # Use ffprobe to get video duration
            probe = ffmpeg.probe(str(url))
            duration = float(probe['streams'][0]['duration'])
            formatted = self._format_duration(duration)
            
            return duration, formatted
            
        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except KeyError:
            raise VideoProcessingError("Could not extract duration from video")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
    
    def capture_screenshot(self, url: str, timestamp: float) -> Tuple[str, float]:
        """
        Capture screenshot at specific timestamp
        
        Args:
            url: Video URL
            timestamp: Timestamp in seconds
            
        Returns:
            Tuple of (output_filename, actual_timestamp)
            
        Raises:
            VideoProcessingError: If unable to capture screenshot
        """
        try:
            self._validate_url(url)
            
            # Generate unique filename
            filename = f"screenshot_{uuid.uuid4().hex[:8]}_{int(timestamp)}.png"
            output_path = os.path.join(self.output_dir, filename)
            
            # Capture screenshot using FFmpeg
            (
                ffmpeg
                .input(str(url), ss=timestamp)
                .output(output_path, vframes=1, format='png')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )
            
            return filename, timestamp
            
        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
    
    def generate_thumbnail(self, url: str) -> Tuple[str, float, float]:
        """
        Generate thumbnail from middle of video
        
        Args:
            url: Video URL
            
        Returns:
            Tuple of (output_filename, thumbnail_timestamp, total_duration)
            
        Raises:
            VideoProcessingError: If unable to generate thumbnail
        """
        try:
            # First get video duration
            duration, _ = self.get_video_duration(url)
            
            # Calculate middle timestamp (50% of duration)
            thumbnail_timestamp = duration / 2
            
            # Generate unique filename
            filename = f"thumbnail_{uuid.uuid4().hex[:8]}.png"
            output_path = os.path.join(self.output_dir, filename)
            
            # Capture thumbnail using FFmpeg
            (
                ffmpeg
                .input(str(url), ss=thumbnail_timestamp)
                .output(output_path, vframes=1, format='png')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )
            
            return filename, thumbnail_timestamp, duration
            
        except VideoProcessingError:
            # Re-raise video processing errors
            raise
        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
