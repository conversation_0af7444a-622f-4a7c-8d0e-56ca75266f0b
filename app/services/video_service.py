"""
Video processing service using FFmpeg
"""

import os
import tempfile
import uuid
import base64
from datetime import timed<PERSON><PERSON>
from typing import Tuple
import ffmpeg
from urllib.parse import urlparse


class VideoProcessingError(Exception):
    """Custom exception for video processing errors"""
    pass


class VideoService:
    """Service for video processing operations using FFmpeg"""
    
    def __init__(self, output_dir: str = "outputs"):
        """
        Initialize video service
        
        Args:
            output_dir: Directory to store output files
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def _validate_url(self, url: str) -> None:
        """
        Validate video URL
        
        Args:
            url: Video URL to validate
            
        Raises:
            VideoProcessingError: If URL is invalid
        """
        parsed = urlparse(str(url))
        if not parsed.scheme or not parsed.netloc:
            raise VideoProcessingError("Invalid URL format")
    
    def _format_duration(self, seconds: float) -> str:
        """
        Format duration in seconds to HH:MM:SS format
        
        Args:
            seconds: Duration in seconds
            
        Returns:
            Formatted duration string
        """
        td = timedelta(seconds=seconds)
        hours, remainder = divmod(td.total_seconds(), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
    
    def get_video_duration(self, url: str) -> Tuple[float, str]:
        """
        Get video duration using FFmpeg
        
        Args:
            url: Video URL
            
        Returns:
            Tuple of (duration_in_seconds, formatted_duration)
            
        Raises:
            VideoProcessingError: If unable to get duration
        """
        try:
            self._validate_url(url)
            
            # Use ffprobe to get video duration
            probe = ffmpeg.probe(str(url))
            duration = float(probe['streams'][0]['duration'])
            formatted = self._format_duration(duration)
            
            return duration, formatted
            
        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except KeyError:
            raise VideoProcessingError("Could not extract duration from video")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
    
    def capture_screenshot(self, url: str, timestamp: float) -> Tuple[str, float]:
        """
        Capture screenshot at specific timestamp and return as base64

        Args:
            url: Video URL
            timestamp: Timestamp in seconds

        Returns:
            Tuple of (base64_encoded_image, actual_timestamp)

        Raises:
            VideoProcessingError: If unable to capture screenshot
        """
        try:
            self._validate_url(url)

            # Use temporary file for screenshot
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Capture screenshot using FFmpeg to temporary file
                (
                    ffmpeg
                    .input(str(url), ss=timestamp)
                    .output(temp_path, vframes=1, format='png')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )

                # Read the image file and encode to base64
                with open(temp_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_encoded = base64.b64encode(image_data).decode('utf-8')

                return base64_encoded, timestamp

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
    
    def generate_thumbnail(self, url: str) -> Tuple[str, float, float]:
        """
        Generate thumbnail from middle of video and return as base64

        Args:
            url: Video URL

        Returns:
            Tuple of (base64_encoded_image, thumbnail_timestamp, total_duration)

        Raises:
            VideoProcessingError: If unable to generate thumbnail
        """
        try:
            # First get video duration
            duration, _ = self.get_video_duration(url)

            # Calculate middle timestamp (50% of duration)
            thumbnail_timestamp = duration / 2

            # Use temporary file for thumbnail
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Capture thumbnail using FFmpeg to temporary file
                (
                    ffmpeg
                    .input(str(url), ss=thumbnail_timestamp)
                    .output(temp_path, vframes=1, format='png')
                    .overwrite_output()
                    .run(capture_stdout=True, capture_stderr=True)
                )

                # Read the image file and encode to base64
                with open(temp_path, 'rb') as image_file:
                    image_data = image_file.read()
                    base64_encoded = base64.b64encode(image_data).decode('utf-8')

                return base64_encoded, thumbnail_timestamp, duration

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except VideoProcessingError:
            # Re-raise video processing errors
            raise
        except ffmpeg.Error as e:
            raise VideoProcessingError(f"FFmpeg error: {e.stderr.decode() if e.stderr else str(e)}")
        except Exception as e:
            raise VideoProcessingError(f"Unexpected error: {str(e)}")
