# Video Processing API

A FastAPI-based service for video processing using FFmpeg. This service provides endpoints for extracting video duration, capturing screenshots at specific timestamps, and generating thumbnails.

## Features

- **Video Duration**: Extract total duration from video URLs
- **Screenshot Capture**: Capture screenshots at specific timestamps
- **Thumbnail Generation**: Automatically generate thumbnails from video middle point
- **Multiple Format Support**: Supports various video formats through FFmpeg
- **Containerized Deployment**: Docker support with Python 3.13 and uv package manager
- **Comprehensive Error Handling**: Proper validation and error responses
- **Interactive API Documentation**: Auto-generated OpenAPI/Swagger docs

## Quick Start

### Using Docker (Recommended)

1. **Build the Docker image:**
   ```bash
   docker build -t video-processing-api .
   ```

2. **Run the container:**
   ```bash
   docker run -p 8000:8000 video-processing-api
   ```

3. **Access the API:**
   - API: http://localhost:8000
   - Interactive docs: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc

### Local Development

1. **Install dependencies:**
   ```bash
   uv sync
   ```

2. **Run the development server:**
   ```bash
   uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## API Endpoints

### 1. Get Video Duration
**GET** `/video/duration`

Extract and return the total duration of a video.

**Parameters:**
- `url` (query): Video URL to analyze

**Example:**
```bash
curl "http://localhost:8000/video/duration?url=https://example.com/video.mp4"
```

**Response:**
```json
{
  "duration": 120.5,
  "formatted_duration": "00:02:00"
}
```

### 2. Capture Screenshot
**POST** `/video/screenshot`

Capture a screenshot from a video at a specific timestamp.

**Request Body:**
```json
{
  "url": "https://example.com/video.mp4",
  "timestamp": 30.5
}
```

**Example:**
```bash
curl -X POST "http://localhost:8000/video/screenshot" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/video.mp4", "timestamp": 30.5}'
```

**Response:**
```json
{
  "success": true,
  "filename": "screenshot_abc12345_30.png",
  "timestamp": 30.5,
  "message": "Screenshot captured successfully"
}
```

### 3. Generate Thumbnail
**GET** `/video/thumbnail`

Generate a thumbnail from the middle of a video (50% of total duration).

**Parameters:**
- `url` (query): Video URL to generate thumbnail from

**Example:**
```bash
curl "http://localhost:8000/video/thumbnail?url=https://example.com/video.mp4"
```

**Response:**
```json
{
  "success": true,
  "filename": "thumbnail_def67890.png",
  "timestamp": 60.25,
  "duration": 120.5,
  "message": "Thumbnail generated successfully"
}
```

### 4. Download Generated Files
**GET** `/video/download/{filename}`

Download a generated screenshot or thumbnail file.

**Example:**
```bash
curl "http://localhost:8000/video/download/screenshot_abc12345_30.png" --output screenshot.png
```

## Error Handling

The API provides comprehensive error handling with appropriate HTTP status codes:

- **400 Bad Request**: Invalid URL, invalid timestamp, or video processing errors
- **404 Not Found**: File not found (for download endpoint)
- **500 Internal Server Error**: Unexpected server errors

**Error Response Format:**
```json
{
  "detail": "Error description"
}
```

## Supported Video Formats

The service supports all video formats that FFmpeg can process, including:
- MP4, AVI, MOV, MKV, WebM
- Various codecs (H.264, H.265, VP8, VP9, etc.)
- Both local files and remote URLs (HTTP/HTTPS)

## Configuration

### Environment Variables

- `PYTHONUNBUFFERED=1`: Ensure Python output is not buffered
- `PYTHONDONTWRITEBYTECODE=1`: Prevent Python from writing .pyc files

### Output Directory

Generated screenshots and thumbnails are stored in the `outputs/` directory by default.

## Development

### Project Structure

```
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application
│   ├── models/
│   │   ├── __init__.py
│   │   └── video.py         # Pydantic models
│   ├── routers/
│   │   ├── __init__.py
│   │   └── video.py         # API endpoints
│   └── services/
│       ├── __init__.py
│       └── video_service.py # FFmpeg operations
├── outputs/                 # Generated files directory
├── Dockerfile
├── pyproject.toml
└── README.md
```

### Dependencies

- **FastAPI**: Modern web framework for building APIs
- **uvicorn**: ASGI server for running FastAPI
- **pydantic**: Data validation using Python type annotations
- **ffmpeg-python**: Python bindings for FFmpeg
- **python-multipart**: For handling multipart form data

## Health Checks

The service includes health check endpoints:

- **GET** `/`: Basic status check
- **GET** `/health`: Detailed health check (used by Docker)

## License

This project is licensed under the MIT License.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## Support

For issues and questions, please create an issue in the repository.